# Meet the Team Section | devChallenges

<div align="center">
   Solution for a challenge <a href="https://devchallenges.io/challenge/meet-the-team-section-challenge" target="_blank">Meet the Team Section</a> from <a href="http://devchallenges.io" target="_blank">devChallenges.io</a>.
</div>

<div align="center">
  <h3>
    <a href="file:///c:/Users/<USER>/Desktop/our-team-layout-master/index.html">
      Demo
    </a>
    <span> | </span>
    <a href="https://github.com/your-username/our-team-layout">
      Solution
    </a>
    <span> | </span>
    <a href="https://devchallenges.io/challenge/meet-the-team-section-challenge">
      Challenge
    </a>
  </h3>
</div>

![Team Section Preview](./thumbnail.jpg)

## Table of Contents

- [Overview](#overview)
  - [Built With](#built-with)
  - [Features](#features)
  - [What I Learned](#what-i-learned)
- [How to Use](#how-to-use)
- [Contact](#contact)
- [Acknowledgements](#acknowledgements)

## Overview

This project is a responsive "Meet the Team" section built as part of a devChallenges.io challenge. The design features a modern, clean layout showcasing team members with their photos, names, and roles, along with an interactive call-to-action card.

### Built With

- **Semantic HTML5** markup for accessibility and SEO
- **CSS3** with modern features:
  - CSS Custom Properties (CSS Variables)
  - CSS Grid for responsive layouts
  - Flexbox for component alignment
  - CSS Gradients and animations
  - Media queries for responsive design
- **Google Fonts** (Inter) for typography
- **Progressive Enhancement** approach
- **Accessibility** features (focus states, reduced motion support)

### Features

- ✅ **Fully Responsive Design** - Works seamlessly on desktop, tablet, and mobile devices
- ✅ **Modern CSS Grid Layout** - Adaptive grid that adjusts to different screen sizes
- ✅ **Interactive Elements** - Hover effects and smooth transitions
- ✅ **Accessibility Features**:
  - Semantic HTML structure
  - Proper focus management
  - Reduced motion support for users with vestibular disorders
  - High contrast mode support
- ✅ **Performance Optimized**:
  - Lazy loading for images
  - Optimized CSS with custom properties
  - Minimal external dependencies
- ✅ **Professional Design**:
  - Clean, modern aesthetic
  - Consistent spacing and typography
  - Gradient accents and subtle animations
  - Featured team member highlighting

### What I Learned

During this project, I focused on implementing modern CSS techniques and best practices:

**CSS Grid Mastery**: Used `grid-template-columns: repeat(auto-fit, minmax(280px, 1fr))` to create a truly responsive grid that adapts to any screen size without media queries for the basic layout.

**CSS Custom Properties**: Implemented a comprehensive design system using CSS variables for colors, spacing, typography, and other design tokens, making the codebase maintainable and consistent.

**Accessibility First**: Incorporated accessibility features from the start, including proper focus management, reduced motion preferences, and high contrast support.

**Progressive Enhancement**: Built the layout to work without JavaScript, ensuring it's accessible to all users regardless of their device capabilities.

## How to Use

1. **Clone or Download** the project files
2. **Open `index.html`** in your web browser
3. **For Development**:
   - Use a local server for best results (e.g., Live Server extension in VS Code)
   - Modify `styles.css` to customize the design
   - Replace team member images in the `resources` folder

### File Structure
```
our-team-layout-master/
├── index.html          # Main HTML file
├── styles.css          # Main stylesheet
├── resources/          # Images and assets
│   ├── person_1.png    # Team member photos
│   ├── person_2.png
│   ├── ...
│   └── favicon.ico
└── design/             # Reference designs
    ├── Desktop_1350px.jpg
    ├── Tablet_1024px.jpg
    └── Mobile_412px.jpg
```

## Contact

- **Coded by**: [Ayokanmi Adejola](#)
- **Challenge by**: [devChallenges.io](https://devchallenges.io?ref=challenge)

## Acknowledgements

- [devChallenges.io](https://devchallenges.io/) for providing this excellent challenge
- [Google Fonts](https://fonts.google.com/) for the Inter font family
- [CSS Grid Guide](https://css-tricks.com/snippets/css/complete-guide-grid/) for grid layout reference
- [MDN Web Docs](https://developer.mozilla.org/) for CSS and accessibility documentation
