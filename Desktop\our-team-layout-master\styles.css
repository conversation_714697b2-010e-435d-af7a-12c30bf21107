:root {
  --primary-color: #6366f1;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --background: #ffffff;
  --surface: #f9fafb;
  --border: #e5e7eb;
  --gradient-start: #6366f1;
  --gradient-end: #8b5cf6;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  
  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Background decorations */
.background-decor {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.background-decor::before {
  content: '';
  position: absolute;
  top: -10%;
  right: -10%;
  width: 40%;
  height: 60%;
  background: url('resources/Background_decor.svg') no-repeat;
  background-size: contain;
  opacity: 0.1;
}

.background-decor::after {
  content: '';
  position: absolute;
  bottom: -10%;
  left: -10%;
  width: 30%;
  height: 40%;
  background: url('resources/Gradient.svg') no-repeat;
  background-size: contain;
  opacity: 0.1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-6);
}

.author-info {
  font-size: var(--font-size-sm);
  text-align: center;
  margin-top: var(--space-16);
  color: var(--text-secondary);
  position: relative;
  z-index: 10;
}

.author-info a {
  text-decoration: none;
  color: var(--primary-color);
  font-weight: 500;
}

.author-info a:hover {
  text-decoration: underline;
}

/* Team Section Styles */
.team-section {
  padding: var(--space-20) 0;
  position: relative;
  z-index: 1;
}

.team-header {
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.team-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-2) var(--space-4);
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  border-radius: var(--radius-2xl);
  margin-bottom: var(--space-6);
}

.badge-text {
  color: white;
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.team-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  line-height: 1.2;
}

.team-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: 1.7;
  max-width: 500px;
  margin: 0 auto;
}

/* Team Grid */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-8);
  max-width: 1000px;
  margin: 0 auto;
}

/* Team Member Cards */
.team-member {
  background: var(--background);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  border: 1px solid var(--border);
  position: relative;
  overflow: hidden;
}

.team-member::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.team-member:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.team-member:hover::before {
  transform: scaleX(1);
}

.team-member.featured {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid var(--primary-color);
}

.team-member.featured::before {
  transform: scaleX(1);
}

.member-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto var(--space-4);
  position: relative;
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  padding: 3px;
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  background: var(--background);
}

.member-info {
  text-align: center;
}

.member-name {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.member-role {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  font-weight: 400;
}

/* Call to Action Card */
.cta-card {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 200px;
}

.cta-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cta-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
}

.cta-card:hover::before {
  opacity: 1;
}

.cta-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.cta-title {
  color: white;
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--space-4);
}

.cta-icon {
  width: 24px;
  height: 24px;
  margin: 0 auto;
  filter: brightness(0) invert(1);
  transition: transform 0.3s ease;
}

.cta-card:hover .cta-icon {
  transform: translate(4px, -4px);
}

.cta-icon img {
  width: 100%;
  height: 100%;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: var(--space-4);
  }

  .team-section {
    padding: var(--space-16) 0;
  }

  .team-title {
    font-size: var(--font-size-3xl);
  }

  .team-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
  }

  .team-member {
    padding: var(--space-5);
  }

  .cta-card {
    padding: var(--space-6);
    min-height: 180px;
  }
}

@media (max-width: 768px) {
  .team-section {
    padding: var(--space-12) 0;
  }

  .team-header {
    margin-bottom: var(--space-12);
  }

  .team-title {
    font-size: var(--font-size-2xl);
  }

  .team-description {
    font-size: var(--font-size-base);
  }

  .team-grid {
    grid-template-columns: 1fr;
    gap: var(--space-5);
  }

  .team-member {
    padding: var(--space-4);
  }

  .member-image {
    width: 60px;
    height: 60px;
  }

  .member-name {
    font-size: var(--font-size-lg);
  }

  .member-role {
    font-size: var(--font-size-sm);
  }

  .cta-card {
    padding: var(--space-5);
    min-height: 150px;
  }

  .cta-title {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--space-3);
  }

  .team-section {
    padding: var(--space-10) 0;
  }

  .team-header {
    margin-bottom: var(--space-10);
  }

  .team-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-4);
  }

  .team-description {
    font-size: var(--font-size-sm);
  }

  .team-grid {
    gap: var(--space-4);
  }

  .team-member {
    padding: var(--space-3);
  }

  .member-image {
    width: 50px;
    height: 50px;
    margin-bottom: var(--space-3);
  }

  .member-name {
    font-size: var(--font-size-base);
  }

  .member-role {
    font-size: var(--font-size-xs);
  }

  .cta-card {
    padding: var(--space-4);
    min-height: 120px;
  }

  .cta-title {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-3);
  }

  .cta-icon {
    width: 20px;
    height: 20px;
  }

  .background-decor::before,
  .background-decor::after {
    display: none;
  }
}

/* Focus and accessibility styles */
.cta-card:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.team-member:focus-within {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .team-member,
  .cta-card,
  .cta-icon,
  .team-member::before {
    transition: none;
  }

  .team-member:hover,
  .cta-card:hover {
    transform: none;
  }

  .cta-card:hover .cta-icon {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .team-member {
    border: 2px solid var(--text-primary);
  }

  .cta-card {
    border: 2px solid var(--text-primary);
  }
}
